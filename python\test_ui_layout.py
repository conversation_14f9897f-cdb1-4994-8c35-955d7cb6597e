#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面布局测试程序
测试新的界面布局和彩色文字显示功能
"""

import tkinter as tk
from tkinter import ttk
import time
import threading

def create_test_window():
    """创建测试窗口"""
    root = tk.Tk()
    root.title("界面布局测试 - 彩色文字显示")
    root.geometry("900x700")
    root.configure(bg='#f0f0f0')
    
    # 主框架
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = ttk.Label(main_frame, text="新界面布局测试", 
                           font=('微软雅黑', 18, 'bold'))
    title_label.pack(pady=(0, 20))
    
    # 模拟左侧控制面板
    left_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
    left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
    
    # 添加一些控制元素
    ttk.Label(left_frame, text="串口设置", font=('微软雅黑', 10, 'bold')).pack(anchor="w", pady=(0, 5))
    ttk.Label(left_frame, text="端口: COM3").pack(anchor="w")
    ttk.Label(left_frame, text="波特率: 115200").pack(anchor="w")
    ttk.Label(left_frame, text="模式: 演示模式").pack(anchor="w", pady=(0, 10))
    
    ttk.Button(left_frame, text="连接", width=15).pack(pady=2)
    ttk.Button(left_frame, text="断开", width=15).pack(pady=2)
    
    # 右侧显示区域（新布局）
    right_container = ttk.Frame(main_frame)
    right_container.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # 上半部分：系统状态监控
    protocol_main_frame = ttk.LabelFrame(right_container, text="系统状态监控", padding=15)
    protocol_main_frame.pack(fill=tk.X, pady=(0, 10))
    
    # 创建网格布局
    protocol_grid = ttk.Frame(protocol_main_frame)
    protocol_grid.pack(fill=tk.X)
    
    # 物块颜色状态区域
    color_section = ttk.Frame(protocol_grid)
    color_section.grid(row=0, column=0, padx=20, pady=10, sticky="w")
    
    color_title = ttk.Label(color_section, text="物块识别", font=('微软雅黑', 12, 'bold'))
    color_title.pack(anchor="w")
    
    block_color_label = tk.Label(color_section, text="红色物块", 
                                font=('微软雅黑', 14, 'bold'), 
                                foreground="#DC143C", background="#f0f0f0")
    block_color_label.pack(anchor="w", pady=(5, 0))
    
    # 机械臂状态区域
    arm_section = ttk.Frame(protocol_grid)
    arm_section.grid(row=0, column=1, padx=20, pady=10, sticky="w")
    
    arm_title = ttk.Label(arm_section, text="机械臂状态", font=('微软雅黑', 12, 'bold'))
    arm_title.pack(anchor="w")
    
    arm_status_label = tk.Label(arm_section, text="抓取状态", 
                               font=('微软雅黑', 14, 'bold'), 
                               foreground="#9932CC", background="#f0f0f0")
    arm_status_label.pack(anchor="w", pady=(5, 0))
    
    # 协议信息显示区域
    info_section = ttk.Frame(protocol_grid)
    info_section.grid(row=1, column=0, columnspan=2, pady=(15, 5), sticky="ew")
    
    info_title = ttk.Label(info_section, text="协议信息", font=('微软雅黑', 10, 'bold'))
    info_title.pack(anchor="w")
    
    protocol_info_label = ttk.Label(info_section, text="最后更新: 14:30:25 | 物块: 红色物块 | 状态: 抓取状态", 
                                   font=('微软雅黑', 9), foreground="#333333")
    protocol_info_label.pack(anchor="w", pady=(2, 0))
    
    # 配置网格权重
    protocol_grid.columnconfigure(0, weight=1)
    protocol_grid.columnconfigure(1, weight=1)
    
    # 下半部分：舵机状态显示（简化版）
    servo_frame = ttk.LabelFrame(right_container, text="舵机状态", padding=10)
    servo_frame.pack(fill=tk.BOTH, expand=True)
    
    # 创建简化的舵机状态显示
    servo_grid = ttk.Frame(servo_frame)
    servo_grid.pack(fill=tk.BOTH, expand=True)
    
    # 创建6个舵机状态卡片
    for i in range(6):
        row = i // 3
        col = i % 3
        
        servo_card = ttk.Frame(servo_grid, relief="raised", borderwidth=1)
        servo_card.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
        
        # 舵机标题
        ttk.Label(servo_card, text=f"舵机 {i+1}", font=('微软雅黑', 10, 'bold')).pack(pady=5)
        
        # 模拟数据
        ttk.Label(servo_card, text=f"位置: {120 + i*10:.1f}°").pack()
        ttk.Label(servo_card, text=f"温度: {30 + i:.1f}℃").pack()
        ttk.Label(servo_card, text=f"电压: {12.0 + i*0.1:.1f}V").pack()
        
        # 状态指示
        status_color = "green" if i % 2 == 0 else "orange"
        status_label = tk.Label(servo_card, text="●", foreground=status_color)
        status_label.pack(pady=2)
    
    # 配置舵机网格权重
    for i in range(2):
        servo_grid.rowconfigure(i, weight=1)
    for i in range(3):
        servo_grid.columnconfigure(i, weight=1)
    
    # 添加颜色测试按钮
    test_frame = ttk.Frame(main_frame)
    test_frame.pack(fill=tk.X, pady=(10, 0))
    
    ttk.Label(test_frame, text="颜色测试:", font=('微软雅黑', 10, 'bold')).pack(side=tk.LEFT)
    
    def change_color(color_code, status_code):
        """改变显示颜色"""
        color_map = {
            0: ("未识别到物块", "#808080"),
            1: ("红色物块", "#DC143C"),
            2: ("绿色物块", "#228B22"),
            3: ("蓝色物块", "#1E90FF")
        }
        
        status_map = {
            0: ("检测状态", "#FF8C00"),
            1: ("抓取状态", "#9932CC"),
            2: ("运输状态", "#8B4513"),
            3: ("分拣状态", "#006400")
        }
        
        if color_code in color_map:
            text, color = color_map[color_code]
            block_color_label.config(text=text, foreground=color)
        
        if status_code in status_map:
            text, color = status_map[status_code]
            arm_status_label.config(text=text, foreground=color)
        
        # 更新协议信息
        current_time = time.strftime("%H:%M:%S")
        color_text = color_map.get(color_code, ("未知", ""))[0]
        status_text = status_map.get(status_code, ("未知", ""))[0]
        info_text = f"最后更新: {current_time} | 物块: {color_text} | 状态: {status_text}"
        protocol_info_label.config(text=info_text)
    
    # 物块颜色测试按钮
    ttk.Button(test_frame, text="未识别", width=8,
              command=lambda: change_color(0, None)).pack(side=tk.LEFT, padx=2)
    ttk.Button(test_frame, text="红色", width=8,
              command=lambda: change_color(1, None)).pack(side=tk.LEFT, padx=2)
    ttk.Button(test_frame, text="绿色", width=8,
              command=lambda: change_color(2, None)).pack(side=tk.LEFT, padx=2)
    ttk.Button(test_frame, text="蓝色", width=8,
              command=lambda: change_color(3, None)).pack(side=tk.LEFT, padx=2)
    
    # 分隔符
    ttk.Separator(test_frame, orient='vertical').pack(side=tk.LEFT, fill='y', padx=10)
    
    # 机械臂状态测试按钮
    ttk.Button(test_frame, text="检测", width=8,
              command=lambda: change_color(None, 0)).pack(side=tk.LEFT, padx=2)
    ttk.Button(test_frame, text="抓取", width=8,
              command=lambda: change_color(None, 1)).pack(side=tk.LEFT, padx=2)
    ttk.Button(test_frame, text="运输", width=8,
              command=lambda: change_color(None, 2)).pack(side=tk.LEFT, padx=2)
    ttk.Button(test_frame, text="分拣", width=8,
              command=lambda: change_color(None, 3)).pack(side=tk.LEFT, padx=2)
    
    # 自动演示
    auto_demo_running = [False]  # 使用列表以便在闭包中修改
    
    def auto_demo():
        """自动演示不同颜色"""
        counter = 0
        while auto_demo_running[0]:
            color_code = counter % 4
            status_code = (counter + 1) % 4
            root.after(0, lambda: change_color(color_code, status_code))
            time.sleep(2)
            counter += 1
    
    def toggle_auto_demo():
        """切换自动演示"""
        if auto_demo_running[0]:
            auto_demo_running[0] = False
            auto_btn.config(text="开始自动演示")
        else:
            auto_demo_running[0] = True
            auto_btn.config(text="停止自动演示")
            demo_thread = threading.Thread(target=auto_demo)
            demo_thread.daemon = True
            demo_thread.start()
    
    ttk.Separator(test_frame, orient='vertical').pack(side=tk.LEFT, fill='y', padx=10)
    auto_btn = ttk.Button(test_frame, text="开始自动演示", command=toggle_auto_demo)
    auto_btn.pack(side=tk.LEFT, padx=5)
    
    return root

def main():
    """主函数"""
    print("启动界面布局测试程序...")
    print("=" * 50)
    print("功能说明:")
    print("1. 展示新的界面布局设计")
    print("2. 演示彩色文字显示效果")
    print("3. 测试不同状态的颜色方案")
    print("4. 验证界面响应性和美观度")
    print("=" * 50)
    
    root = create_test_window()
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("程序已停止")

if __name__ == "__main__":
    main()
