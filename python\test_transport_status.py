#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运输状态测试程序
验证程序是否能够正确生成和处理运输状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from program_demo import ProtocolHand<PERSON>, DemoCommunicator
import time

def test_transport_status_protocol():
    """测试运输状态协议处理"""
    print("=" * 70)
    print("1. 运输状态协议处理测试")
    print("=" * 70)
    
    handler = ProtocolHandler()
    
    print("测试运输状态数据包创建和解析:")
    print("-" * 50)
    
    # 创建运输状态数据包
    transport_packet = handler.create_protocol_packet(0x02, 0x02)  # 机械臂状态指令，运输状态
    packet_hex = ' '.join([f'{b:02X}' for b in transport_packet])
    
    print(f"运输状态数据包: {packet_hex}")
    print(f"帧头: 0x{transport_packet[0]:02X} 0x{transport_packet[1]:02X}")
    print(f"指令: 0x{transport_packet[2]:02X} (机械臂状态)")
    print(f"数据: 0x{transport_packet[3]:02X} (运输状态)")
    print(f"校验和: 0x{transport_packet[4]:02X}")
    
    # 验证校验和
    expected_checksum = (0x02 + 0x02) & 0xFF
    actual_checksum = transport_packet[4]
    checksum_ok = expected_checksum == actual_checksum
    
    print(f"校验和验证: {'✅' if checksum_ok else '❌'} (期望: 0x{expected_checksum:02X}, 实际: 0x{actual_checksum:02X})")
    
    # 解析数据包
    result = handler.parse_protocol_packet(transport_packet)
    
    if result:
        print(f"解析结果: {result['description']} ✅")
        print(f"指令类型: {result['type']}")
        print(f"时间戳: {result['timestamp']}")
    else:
        print("解析失败 ❌")
        return False
    
    # 检查状态更新
    print(f"\n状态更新检查:")
    print(f"机械臂状态值: {handler.latest_protocol_data['arm_status']}")
    print(f"状态文字: {handler.get_arm_status_text()}")
    print(f"状态颜色: {handler.get_arm_status_color()}")
    
    # 验证状态是否正确
    status_correct = (
        handler.latest_protocol_data['arm_status'] == 2 and
        handler.get_arm_status_text() == "运输状态" and
        handler.get_arm_status_color() == "#8B4513"
    )
    
    print(f"状态更新正确: {'✅' if status_correct else '❌'}")
    
    return checksum_ok and result is not None and status_correct

def test_demo_mode_transport():
    """测试演示模式中的运输状态生成"""
    print("\n" + "=" * 70)
    print("2. 演示模式运输状态生成测试")
    print("=" * 70)
    
    # 创建演示通信器
    demo_comm = DemoCommunicator()
    
    print("检查演示模式配置:")
    print("-" * 50)
    print(f"协议处理器存在: {'✅' if hasattr(demo_comm, 'protocol_handler') else '❌'}")
    
    if not hasattr(demo_comm, 'protocol_handler'):
        return False
    
    # 手动设置运输状态
    demo_comm.protocol_handler.latest_protocol_data['arm_status'] = 2
    demo_comm.protocol_handler.latest_protocol_data['last_update_time'] = time.time()
    demo_comm.protocol_handler.is_initialized = False
    
    print("手动设置运输状态:")
    print(f"状态值: {demo_comm.protocol_handler.latest_protocol_data['arm_status']}")
    print(f"状态文字: {demo_comm.protocol_handler.get_arm_status_text()}")
    print(f"状态颜色: {demo_comm.protocol_handler.get_arm_status_color()}")
    
    # 验证演示模式的随机生成范围
    print(f"\n演示模式随机生成范围检查:")
    print("-" * 50)
    
    # 模拟多次随机生成，检查是否包含运输状态
    import random
    transport_generated = False
    
    for i in range(100):  # 测试100次
        random_status = random.randint(0, 3)
        if random_status == 2:  # 运输状态
            transport_generated = True
            break
    
    print(f"随机生成范围: 0-3 (包含运输状态) {'✅' if transport_generated else '❌'}")
    
    # 测试所有可能的机械臂状态
    print(f"\n所有机械臂状态测试:")
    print("-" * 50)
    
    all_statuses = []
    for status_value in range(4):
        demo_comm.protocol_handler.latest_protocol_data['arm_status'] = status_value
        demo_comm.protocol_handler.is_initialized = False
        
        text = demo_comm.protocol_handler.get_arm_status_text()
        color = demo_comm.protocol_handler.get_arm_status_color()
        
        all_statuses.append((status_value, text, color))
        print(f"状态 {status_value}: {text} (颜色: {color})")
    
    # 验证运输状态是否在列表中
    transport_in_list = any(status[1] == "运输状态" for status in all_statuses)
    print(f"\n运输状态包含在状态列表中: {'✅' if transport_in_list else '❌'}")
    
    return transport_generated and transport_in_list

def test_ui_transport_display():
    """测试UI中运输状态的显示"""
    print("\n" + "=" * 70)
    print("3. UI运输状态显示测试")
    print("=" * 70)
    
    handler = ProtocolHandler()
    
    # 设置为运输状态
    handler.latest_protocol_data['arm_status'] = 2
    handler.latest_protocol_data['last_update_time'] = time.time()
    handler.is_initialized = False
    
    print("UI显示信息:")
    print("-" * 50)
    
    # 获取UI应该显示的信息
    display_text = handler.get_arm_status_text()
    display_color = handler.get_arm_status_color()
    
    print(f"显示文字: '{display_text}'")
    print(f"显示颜色: {display_color}")
    print(f"颜色名称: 马鞍棕色")
    
    # 验证显示信息
    display_correct = (
        display_text == "运输状态" and
        display_color == "#8B4513"
    )
    
    print(f"UI显示正确: {'✅' if display_correct else '❌'}")
    
    # 模拟UI更新逻辑
    print(f"\nUI更新逻辑模拟:")
    print("-" * 50)
    print("假设的UI标签更新:")
    print(f"  self.arm_status_label.config(text='{display_text}', foreground='{display_color}')")
    
    return display_correct

def test_transport_in_all_modes():
    """测试运输状态在所有模式中的支持"""
    print("\n" + "=" * 70)
    print("4. 运输状态全模式支持测试")
    print("=" * 70)
    
    print("机械臂状态完整列表:")
    print("-" * 50)
    
    status_definitions = [
        (0, "检测状态", "#FF8C00", "深橙色"),
        (1, "抓取状态", "#9932CC", "深兰花紫"),
        (2, "运输状态", "#8B4513", "马鞍棕色"),  # 重点检查
        (3, "分拣状态", "#006400", "深绿色")
    ]
    
    handler = ProtocolHandler()
    all_correct = True
    
    for value, expected_text, expected_color, color_name in status_definitions:
        # 设置状态
        handler.latest_protocol_data['arm_status'] = value
        handler.is_initialized = False
        handler.latest_protocol_data['last_update_time'] = time.time()
        
        # 获取实际值
        actual_text = handler.get_arm_status_text()
        actual_color = handler.get_arm_status_color()
        
        # 验证
        text_ok = actual_text == expected_text
        color_ok = actual_color == expected_color
        
        status = "✅" if text_ok and color_ok else "❌"
        print(f"状态 {value}: {actual_text} | {actual_color} ({color_name}) {status}")
        
        if not (text_ok and color_ok):
            all_correct = False
            print(f"  期望: {expected_text} | {expected_color}")
            print(f"  实际: {actual_text} | {actual_color}")
    
    print(f"\n所有状态定义正确: {'✅' if all_correct else '❌'}")
    
    # 特别验证运输状态
    print(f"\n运输状态特别验证:")
    print("-" * 50)
    
    handler.latest_protocol_data['arm_status'] = 2
    handler.is_initialized = False
    
    transport_text = handler.get_arm_status_text()
    transport_color = handler.get_arm_status_color()
    
    print(f"运输状态文字: '{transport_text}'")
    print(f"运输状态颜色: {transport_color}")
    print(f"协议数据包: {' '.join([f'{b:02X}' for b in handler.create_protocol_packet(0x02, 0x02)])}")
    
    transport_ok = transport_text == "运输状态" and transport_color == "#8B4513"
    print(f"运输状态完全正确: {'✅' if transport_ok else '❌'}")
    
    return all_correct and transport_ok

def main():
    """主测试函数"""
    print("运输状态功能验证测试")
    print("=" * 70)
    print("验证程序是否能够正确生成和处理运输状态")
    print("=" * 70)
    
    # 执行所有测试
    test_results = []
    test_results.append(("协议处理", test_transport_status_protocol()))
    test_results.append(("演示模式", test_demo_mode_transport()))
    test_results.append(("UI显示", test_ui_transport_display()))
    test_results.append(("全模式支持", test_transport_in_all_modes()))
    
    # 汇总结果
    print("\n" + "=" * 70)
    print("测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 70)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 运输状态功能完全正常！")
        print("\n✅ 功能确认:")
        print("  • 运输状态协议数据包创建正确")
        print("  • 运输状态协议解析正确")
        print("  • 演示模式包含运输状态生成")
        print("  • UI界面能正确显示运输状态")
        print("  • 运输状态颜色映射正确")
        
        print("\n📋 运输状态详情:")
        print("  • 协议指令: 0x02 (机械臂状态)")
        print("  • 数据值: 0x02 (运输状态)")
        print("  • 显示文字: '运输状态'")
        print("  • 显示颜色: #8B4513 (马鞍棕色)")
        print("  • 数据包: 77 77 02 02 04")
        
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，运输状态功能可能存在问题。")

if __name__ == "__main__":
    main()
