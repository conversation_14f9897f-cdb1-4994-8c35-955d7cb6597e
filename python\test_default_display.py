#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
默认状态显示测试程序
验证机械臂状态和系统状态显示为"默认状态"
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from program_demo import ProtocolHandler

def test_default_display():
    """测试默认状态显示"""
    print("=" * 70)
    print("默认状态显示测试")
    print("=" * 70)
    
    # 创建新的协议处理器实例
    handler = ProtocolHandler()
    
    print("1. 检查初始化状态")
    print("-" * 50)
    
    print(f"初始化标志: {handler.is_initialized}")
    print(f"最后更新时间: {handler.latest_protocol_data['last_update_time']}")
    
    print("\n2. 测试初始状态显示")
    print("-" * 50)
    
    # 获取初始状态的文字描述
    block_color_text = handler.get_block_color_text()
    arm_status_text = handler.get_arm_status_text()
    system_status_text = handler.get_system_status_text()
    
    print("初始状态文字:")
    print(f"  物块颜色: '{block_color_text}'")
    print(f"  机械臂状态: '{arm_status_text}'")
    print(f"  系统状态: '{system_status_text}'")
    
    # 验证是否显示为"默认状态"
    arm_correct = arm_status_text == "默认状态"
    system_correct = system_status_text == "默认状态"
    
    print("\n验证结果:")
    print(f"  机械臂状态显示正确: {'✅' if arm_correct else '❌'} (期望: '默认状态', 实际: '{arm_status_text}')")
    print(f"  系统状态显示正确: {'✅' if system_correct else '❌'} (期望: '默认状态', 实际: '{system_status_text}')")
    
    print("\n3. 测试初始状态颜色")
    print("-" * 50)
    
    # 获取初始状态的颜色
    block_color_color = handler.get_block_color_color()
    arm_status_color = handler.get_arm_status_color()
    system_status_color = handler.get_system_status_color()
    
    print("初始状态颜色:")
    print(f"  物块颜色: {block_color_color}")
    print(f"  机械臂状态: {arm_status_color}")
    print(f"  系统状态: {system_status_color}")
    
    # 验证默认状态颜色是否为灰色
    arm_color_correct = arm_status_color == "#808080"
    system_color_correct = system_status_color == "#808080"
    
    print("\n颜色验证结果:")
    print(f"  机械臂状态颜色正确: {'✅' if arm_color_correct else '❌'} (期望: '#808080', 实际: '{arm_status_color}')")
    print(f"  系统状态颜色正确: {'✅' if system_color_correct else '❌'} (期望: '#808080', 实际: '{system_status_color}')")
    
    print("\n4. 测试接收数据后的状态变化")
    print("-" * 50)
    
    # 模拟接收机械臂状态数据
    print("模拟接收机械臂状态数据包...")
    packet = handler.create_protocol_packet(0x02, 0x01)  # 抓取状态
    result = handler.parse_protocol_packet(packet)
    
    if result:
        print(f"数据包解析成功: {result['description']}")
        print(f"初始化标志变为: {handler.is_initialized}")
        
        # 检查状态文字是否变化
        new_arm_text = handler.get_arm_status_text()
        new_arm_color = handler.get_arm_status_color()
        
        print(f"机械臂状态变为: '{new_arm_text}' (颜色: {new_arm_color})")
        
        # 验证是否不再显示"默认状态"
        changed_correctly = new_arm_text != "默认状态"
        print(f"状态变化正确: {'✅' if changed_correctly else '❌'}")
    
    print("\n5. 测试重置为默认状态")
    print("-" * 50)
    
    # 重置为初始化状态
    handler.is_initialized = True
    handler.latest_protocol_data['last_update_time'] = 0
    
    # 检查是否重新显示为默认状态
    reset_arm_text = handler.get_arm_status_text()
    reset_system_text = handler.get_system_status_text()
    reset_arm_color = handler.get_arm_status_color()
    reset_system_color = handler.get_system_status_color()
    
    print("重置后的状态:")
    print(f"  机械臂状态: '{reset_arm_text}' (颜色: {reset_arm_color})")
    print(f"  系统状态: '{reset_system_text}' (颜色: {reset_system_color})")
    
    # 验证重置结果
    reset_arm_correct = reset_arm_text == "默认状态" and reset_arm_color == "#808080"
    reset_system_correct = reset_system_text == "默认状态" and reset_system_color == "#808080"
    
    print("\n重置验证结果:")
    print(f"  机械臂状态重置正确: {'✅' if reset_arm_correct else '❌'}")
    print(f"  系统状态重置正确: {'✅' if reset_system_correct else '❌'}")
    
    # 统计测试结果
    all_tests = [
        arm_correct,
        system_correct,
        arm_color_correct,
        system_color_correct,
        changed_correctly,
        reset_arm_correct,
        reset_system_correct
    ]
    
    passed_count = sum(all_tests)
    total_count = len(all_tests)
    
    return passed_count == total_count

def test_ui_expected_display():
    """测试UI预期显示效果"""
    print("\n" + "=" * 70)
    print("UI预期显示效果测试")
    print("=" * 70)
    
    handler = ProtocolHandler()
    
    print("启动时UI应该显示:")
    print("-" * 50)
    print("┌─────────────────────────────────────────────────────────┐")
    print("│                系统状态监控                              │")
    print("├─────────────┬─────────────┬─────────────────────────────┤")
    print("│  物块识别    │  机械臂状态  │      系统状态               │")
    print("│             │             │                            │")
    print("│未识别到物块   │   默认状态   │      默认状态               │")
    print("│  (灰色)     │  (灰色)     │     (灰色)                 │")
    print("└─────────────┴─────────────┴─────────────────────────────┘")
    print("│         协议信息: 等待数据...                            │")
    print("└─────────────────────────────────────────────────────────┘")
    
    print("\n实际获取的文字:")
    print("-" * 50)
    print(f"物块识别: '{handler.get_block_color_text()}'")
    print(f"机械臂状态: '{handler.get_arm_status_text()}'")
    print(f"系统状态: '{handler.get_system_status_text()}'")
    
    print("\n实际获取的颜色:")
    print("-" * 50)
    print(f"物块识别: {handler.get_block_color_color()}")
    print(f"机械臂状态: {handler.get_arm_status_color()}")
    print(f"系统状态: {handler.get_system_status_color()}")
    
    return True

def main():
    """主测试函数"""
    print("默认状态显示测试程序")
    print("=" * 70)
    print("验证机械臂状态和系统状态显示为'默认状态'")
    print("=" * 70)
    
    try:
        # 执行测试
        test1_result = test_default_display()
        test2_result = test_ui_expected_display()
        
        # 汇总结果
        print("\n" + "=" * 70)
        print("测试结果汇总")
        print("=" * 70)
        
        if test1_result and test2_result:
            print("🎉 所有测试通过！默认状态显示功能正常！")
            print("\n✅ 功能确认:")
            print("  • 初始化时机械臂状态显示为'默认状态'")
            print("  • 初始化时系统状态显示为'默认状态'")
            print("  • 默认状态使用灰色显示")
            print("  • 接收数据后状态正确变化")
            print("  • 重置后能正确恢复为默认状态")
            
            print("\n🎨 UI显示效果:")
            print("  • 启动时显示统一的'默认状态'文字")
            print("  • 使用灰色表示未激活状态")
            print("  • 接收数据后显示具体状态和对应颜色")
            print("  • 断开连接后重置为默认状态")
            
            print("\n📋 用户体验:")
            print("  • 用户一眼就能看出系统处于初始状态")
            print("  • '默认状态'比具体状态名称更直观")
            print("  • 灰色表示未激活，符合用户习惯")
            print("  • 状态变化清晰可见")
            
        else:
            print("❌ 部分测试失败，请检查默认状态显示功能")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
