#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
默认状态测试程序
验证协议处理器的默认状态设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from program_demo import ProtocolHandler

def test_default_status():
    """测试默认状态设置"""
    print("=" * 70)
    print("默认状态测试")
    print("=" * 70)
    
    # 创建新的协议处理器实例
    handler = ProtocolHandler()
    
    print("1. 检查初始化默认值")
    print("-" * 50)
    
    # 检查默认数据值
    default_data = handler.latest_protocol_data
    
    print("初始化数据:")
    print(f"  物块颜色 (block_color): {default_data['block_color']}")
    print(f"  机械臂状态 (arm_status): {default_data['arm_status']}")
    print(f"  系统状态 (system_status): {default_data['system_status']}")
    print(f"  最后更新时间: {default_data['last_update_time']}")
    
    print("\n2. 验证默认状态文字描述")
    print("-" * 50)
    
    # 获取默认状态的文字描述
    block_color_text = handler.get_block_color_text()
    arm_status_text = handler.get_arm_status_text()
    system_status_text = handler.get_system_status_text()
    
    print("默认状态文字:")
    print(f"  物块颜色: {block_color_text}")
    print(f"  机械臂状态: {arm_status_text}")
    print(f"  系统状态: {system_status_text}")
    
    print("\n3. 验证默认状态颜色")
    print("-" * 50)
    
    # 获取默认状态的颜色
    block_color_color = handler.get_block_color_color()
    arm_status_color = handler.get_arm_status_color()
    system_status_color = handler.get_system_status_color()
    
    print("默认状态颜色:")
    print(f"  物块颜色: {block_color_color}")
    print(f"  机械臂状态: {arm_status_color}")
    print(f"  系统状态: {system_status_color}")
    
    print("\n4. 验证默认状态的合理性")
    print("-" * 50)
    
    # 验证默认值是否合理
    validations = []
    
    # 物块颜色默认为0（未识别）是合理的
    if default_data['block_color'] == 0:
        validations.append(("物块颜色默认值", True, "0 (未识别) - 合理"))
    else:
        validations.append(("物块颜色默认值", False, f"{default_data['block_color']} - 应该为0"))
    
    # 机械臂状态默认为0（检测状态）是合理的
    if default_data['arm_status'] == 0:
        validations.append(("机械臂状态默认值", True, "0 (检测状态) - 合理"))
    else:
        validations.append(("机械臂状态默认值", False, f"{default_data['arm_status']} - 应该为0"))
    
    # 系统状态默认为2（工作模式）是合理的
    if default_data['system_status'] == 2:
        validations.append(("系统状态默认值", True, "2 (工作模式) - 合理"))
    else:
        validations.append(("系统状态默认值", False, f"{default_data['system_status']} - 应该为2"))
    
    # 最后更新时间默认为0是合理的
    if default_data['last_update_time'] == 0:
        validations.append(("更新时间默认值", True, "0 (未更新) - 合理"))
    else:
        validations.append(("更新时间默认值", False, f"{default_data['last_update_time']} - 应该为0"))
    
    # 显示验证结果
    for name, passed, description in validations:
        status = "✅" if passed else "❌"
        print(f"  {name}: {status} {description}")
    
    print("\n5. 创建默认状态数据包测试")
    print("-" * 50)
    
    # 测试创建默认状态的数据包
    test_packets = [
        (0x01, default_data['block_color'], "物块颜色默认状态"),
        (0x02, default_data['arm_status'], "机械臂默认状态"),
        (0x03, default_data['system_status'], "系统默认状态")
    ]
    
    for cmd, data, description in test_packets:
        packet = handler.create_protocol_packet(cmd, data)
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        
        # 解析数据包验证
        result = handler.parse_protocol_packet(packet)
        
        if result:
            print(f"  {description}: {packet_hex} -> {result['description']} ✅")
        else:
            print(f"  {description}: {packet_hex} -> 解析失败 ❌")
    
    # 统计验证结果
    passed_count = sum(1 for _, passed, _ in validations)
    total_count = len(validations)
    
    return passed_count == total_count

def test_ui_default_display():
    """测试UI默认显示"""
    print("\n" + "=" * 70)
    print("UI默认显示测试")
    print("=" * 70)
    
    handler = ProtocolHandler()
    
    print("UI应该显示的默认状态:")
    print("-" * 50)
    
    # 根据默认值显示UI应该显示的内容
    expected_ui = {
        'block_color': {
            'text': handler.get_block_color_text(),
            'color': handler.get_block_color_color()
        },
        'arm_status': {
            'text': handler.get_arm_status_text(),
            'color': handler.get_arm_status_color()
        },
        'system_status': {
            'text': handler.get_system_status_text(),
            'color': handler.get_system_status_color()
        }
    }
    
    print(f"物块颜色标签: '{expected_ui['block_color']['text']}' (颜色: {expected_ui['block_color']['color']})")
    print(f"机械臂状态标签: '{expected_ui['arm_status']['text']}' (颜色: {expected_ui['arm_status']['color']})")
    print(f"系统状态标签: '{expected_ui['system_status']['text']}' (颜色: {expected_ui['system_status']['color']})")
    
    print("\n预期的协议信息显示:")
    print("-" * 50)
    print("初始状态: '等待数据...' 或 '未连接'")
    print("连接后: '最后更新: HH:MM:SS | 物块: 未识别到物块 | 机械臂: 检测状态 | 系统: 工作模式'")
    
    return True

def main():
    """主测试函数"""
    print("默认状态测试程序")
    print("=" * 70)
    print("验证协议处理器的默认状态设置是否合理")
    print("=" * 70)
    
    try:
        # 执行测试
        test1_result = test_default_status()
        test2_result = test_ui_default_display()
        
        # 汇总结果
        print("\n" + "=" * 70)
        print("测试结果汇总")
        print("=" * 70)
        
        if test1_result and test2_result:
            print("🎉 所有测试通过！默认状态设置合理！")
            print("\n✅ 默认状态确认:")
            print("  • 物块颜色: 0 (未识别到物块) - 灰色显示")
            print("  • 机械臂状态: 0 (检测状态) - 橙色显示")
            print("  • 系统状态: 2 (工作模式) - 绿色显示")
            print("  • 更新时间: 0 (未更新)")
            
            print("\n📋 默认状态的合理性:")
            print("  • 物块颜色默认为'未识别'是合理的，表示初始时没有检测到物块")
            print("  • 机械臂默认为'检测状态'是合理的，表示系统启动后处于观察模式")
            print("  • 系统默认为'工作模式'是合理的，表示系统准备好执行任务")
            print("  • 这些默认值符合实际使用场景的需求")
            
            print("\n🎨 UI显示效果:")
            print("  • 启动时界面显示合理的默认状态")
            print("  • 颜色编码清晰易懂")
            print("  • 用户可以立即了解系统当前状态")
            
        else:
            print("❌ 部分测试失败，请检查默认状态设置")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
