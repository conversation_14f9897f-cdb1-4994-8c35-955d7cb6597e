#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新协议功能测试
验证协议处理器的各项功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from program_demo import ProtocolH<PERSON><PERSON>

def test_protocol_handler():
    """测试协议处理器功能"""
    print("=" * 60)
    print("新协议功能测试")
    print("=" * 60)
    
    # 创建协议处理器
    handler = ProtocolHandler()
    
    print("\n1. 测试校验和计算")
    print("-" * 30)
    
    # 测试校验和计算
    test_cases = [
        (0x01, 0x01),  # 红色物块
        (0x01, 0x02),  # 绿色物块
        (0x02, 0x00),  # 检测状态
        (0x02, 0x03),  # 分拣状态
    ]
    
    for cmd, data in test_cases:
        checksum = handler.calc_protocol_checksum(cmd, data)
        print(f"指令: 0x{cmd:02X}, 数据: 0x{data:02X}, 校验和: 0x{checksum:02X}")
    
    print("\n2. 测试数据包创建")
    print("-" * 30)
    
    for cmd, data in test_cases:
        packet = handler.create_protocol_packet(cmd, data)
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        print(f"指令: 0x{cmd:02X}, 数据: 0x{data:02X} -> 数据包: {packet_hex}")
    
    print("\n3. 测试数据包解析")
    print("-" * 30)
    
    # 测试物块颜色数据包解析
    color_packets = [
        bytes([0x77, 0x77, 0x01, 0x00, 0x01]),  # 未识别
        bytes([0x77, 0x77, 0x01, 0x01, 0x02]),  # 红色
        bytes([0x77, 0x77, 0x01, 0x02, 0x03]),  # 绿色
        bytes([0x77, 0x77, 0x01, 0x03, 0x04]),  # 蓝色
    ]
    
    print("物块颜色数据包解析:")
    for packet in color_packets:
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        result = handler.parse_protocol_packet(packet)
        if result:
            print(f"  {packet_hex} -> {result['description']}")
        else:
            print(f"  {packet_hex} -> 解析失败")
    
    # 测试机械臂状态数据包解析
    status_packets = [
        bytes([0x77, 0x77, 0x02, 0x00, 0x02]),  # 检测
        bytes([0x77, 0x77, 0x02, 0x01, 0x03]),  # 抓取
        bytes([0x77, 0x77, 0x02, 0x02, 0x04]),  # 运输
        bytes([0x77, 0x77, 0x02, 0x03, 0x05]),  # 分拣
    ]
    
    print("\n机械臂状态数据包解析:")
    for packet in status_packets:
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        result = handler.parse_protocol_packet(packet)
        if result:
            print(f"  {packet_hex} -> {result['description']}")
        else:
            print(f"  {packet_hex} -> 解析失败")
    
    print("\n4. 测试错误数据包处理")
    print("-" * 30)
    
    # 测试错误的数据包
    error_packets = [
        bytes([0x55, 0x55, 0x01, 0x01, 0x02]),  # 错误帧头
        bytes([0x77, 0x77, 0x01, 0x01, 0x03]),  # 错误校验和
        bytes([0x77, 0x77, 0x05, 0x01, 0x06]),  # 未知指令
        bytes([0x77, 0x77, 0x01]),              # 数据包太短
    ]
    
    for packet in error_packets:
        packet_hex = ' '.join([f'{b:02X}' for b in packet])
        result = handler.parse_protocol_packet(packet)
        if result:
            print(f"  {packet_hex} -> {result['description']}")
        else:
            print(f"  {packet_hex} -> 解析失败 (预期)")
    
    print("\n5. 测试状态获取方法")
    print("-" * 30)
    
    # 设置不同的状态并测试获取方法
    test_states = [
        ('block_color', [0, 1, 2, 3]),
        ('arm_status', [0, 1, 2, 3])
    ]
    
    for state_type, values in test_states:
        print(f"\n{state_type} 状态测试:")
        for value in values:
            handler.latest_protocol_data[state_type] = value
            
            if state_type == 'block_color':
                text = handler.get_block_color_text()
                color = handler.get_block_color_color()
                print(f"  值: {value} -> 文字: {text}, 颜色: {color}")
            else:
                text = handler.get_arm_status_text()
                color = handler.get_arm_status_color()
                print(f"  值: {value} -> 文字: {text}, 颜色: {color}")
    
    print("\n6. 测试完整的数据包处理流程")
    print("-" * 30)
    
    # 模拟完整的数据包处理流程
    print("模拟接收物块颜色数据包...")
    packet = handler.create_protocol_packet(0x01, 0x02)  # 绿色物块
    result = handler.parse_protocol_packet(packet)
    
    if result:
        print(f"接收成功: {result['description']}")
        print(f"当前物块颜色: {handler.get_block_color_text()}")
        print(f"显示颜色: {handler.get_block_color_color()}")
    
    print("\n模拟接收机械臂状态数据包...")
    packet = handler.create_protocol_packet(0x02, 0x01)  # 抓取状态
    result = handler.parse_protocol_packet(packet)
    
    if result:
        print(f"接收成功: {result['description']}")
        print(f"当前机械臂状态: {handler.get_arm_status_text()}")
        print(f"显示颜色: {handler.get_arm_status_color()}")
    
    print("\n" + "=" * 60)
    print("测试完成！所有功能正常工作。")
    print("=" * 60)

def main():
    """主函数"""
    try:
        test_protocol_handler()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
