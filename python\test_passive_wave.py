#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试被动监听模式的温度和电压波动功能
"""

import time
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from program_demo import PassiveCommunicator

def test_passive_wave_simulation():
    """测试被动监听模式的波动模拟"""
    print("=== 被动监听模式温度电压波动测试 ===")
    
    # 创建被动通信实例（不需要真实串口）
    passive_comm = PassiveCommunicator("COM_TEST", 115200)
    
    # 初始化基础值
    passive_comm._initialize_base_values()
    
    print("\n初始状态:")
    for i in range(6):
        print(f"舵机{i+1}: 温度={passive_comm.latest_data['temperature'][i]:.2f}°C, "
              f"电压={passive_comm.latest_data['voltage'][i]:.2f}V")
    
    print("\n开始波动模拟...")
    print("观察温度和电压的动态变化（按Ctrl+C停止）")
    
    try:
        # 模拟动画循环中的波动更新逻辑
        import random
        import math
        
        wave_counter = 0
        
        while True:
            wave_counter += 1
            
            # 更新环境模拟相位
            passive_comm.ambient_temp_phase += 0.002
            passive_comm.load_simulation_phase += 0.01
            passive_comm.power_fluctuation_phase += 0.008
            
            # 计算环境影响因子
            ambient_effect = math.sin(passive_comm.ambient_temp_phase) * 0.3
            load_effect = math.sin(passive_comm.load_simulation_phase) * 0.15
            power_effect = math.sin(passive_comm.power_fluctuation_phase) * 0.1
            
            # 更新温度波动
            for i in range(6):
                # 更新相位
                passive_comm.temp_wave_phases[i] += 0.04 + random.uniform(-0.01, 0.01)
                passive_comm.temp_secondary_phases[i] += 0.15 + random.uniform(-0.02, 0.02)
                passive_comm.temp_trend_phases[i] += 0.001 + random.uniform(-0.0005, 0.0005)

                # 计算波动
                temp_main_wave = math.sin(passive_comm.temp_wave_phases[i]) * 0.4 * passive_comm.temp_wave_intensity
                temp_secondary_wave = math.sin(passive_comm.temp_secondary_phases[i]) * 0.15
                temp_trend_wave = math.sin(passive_comm.temp_trend_phases[i]) * 0.8
                temp_noise = random.uniform(-0.08, 0.08)
                
                # 模拟运动影响
                motion_heat = 0.0
                if i < 2:  # 假设前两个舵机在运动
                    motion_heat = random.uniform(0.1, 0.3)

                # 合成最终温度
                final_temp = (passive_comm.base_temperatures[i] + 
                            temp_main_wave + 
                            temp_secondary_wave + 
                            temp_trend_wave + 
                            ambient_effect + 
                            motion_heat + 
                            temp_noise)
                
                passive_comm.latest_data['temperature'][i] = max(25.0, min(40.0, final_temp))

            # 更新电压波动
            for i in range(6):
                # 更新相位
                passive_comm.volt_wave_phases[i] += 0.035 + random.uniform(-0.008, 0.008)
                passive_comm.volt_secondary_phases[i] += 0.12 + random.uniform(-0.015, 0.015)
                passive_comm.volt_trend_phases[i] += 0.0008 + random.uniform(-0.0003, 0.0003)

                # 计算波动
                volt_main_wave = math.cos(passive_comm.volt_wave_phases[i]) * 0.15 * passive_comm.volt_wave_intensity
                volt_secondary_wave = math.cos(passive_comm.volt_secondary_phases[i]) * 0.08
                volt_trend_wave = math.cos(passive_comm.volt_trend_phases[i]) * 0.25
                volt_noise = random.uniform(-0.03, 0.03)
                
                # 温度相关性影响
                temp_correlation = -(passive_comm.latest_data['temperature'][i] - passive_comm.base_temperatures[i]) * passive_comm.correlation_factor * 0.02
                
                # 负载和电源影响
                load_voltage_effect = load_effect * 0.1
                power_voltage_effect = power_effect * 0.15

                # 合成最终电压
                final_voltage = (passive_comm.base_voltages[i] + 
                               volt_main_wave + 
                               volt_secondary_wave + 
                               volt_trend_wave + 
                               temp_correlation + 
                               load_voltage_effect + 
                               power_voltage_effect + 
                               volt_noise)
                
                passive_comm.latest_data['voltage'][i] = max(10.5, min(13.5, final_voltage))

            # 每50次更新显示一次状态
            if wave_counter % 50 == 0:
                print(f"\n--- 第{wave_counter}次更新 ---")
                for i in range(6):
                    status = "运动中" if i < 2 else "静止"
                    print(f"舵机{i+1}: {passive_comm.latest_data['temperature'][i]:.2f}°C, "
                          f"{passive_comm.latest_data['voltage'][i]:.3f}V, {status}")
                
                # 显示统计信息
                stats = passive_comm.get_wave_statistics()
                if stats:
                    print(f"温度范围: {stats['temperature']['min']:.2f}°C - {stats['temperature']['max']:.2f}°C")
                    print(f"电压范围: {stats['voltage']['min']:.3f}V - {stats['voltage']['max']:.3f}V")
            
            # 模拟20ms的更新间隔
            time.sleep(0.02)
            
    except KeyboardInterrupt:
        print("\n\n测试结束")
        
        # 显示最终统计
        stats = passive_comm.get_wave_statistics()
        if stats:
            print("\n=== 最终统计信息 ===")
            print(f"总更新次数: {wave_counter}")
            print(f"温度统计: 最小={stats['temperature']['min']:.2f}°C, "
                  f"最大={stats['temperature']['max']:.2f}°C, "
                  f"平均={stats['temperature']['avg']:.2f}°C, "
                  f"波动范围={stats['temperature']['range']:.2f}°C")
            print(f"电压统计: 最小={stats['voltage']['min']:.3f}V, "
                  f"最大={stats['voltage']['max']:.3f}V, "
                  f"平均={stats['voltage']['avg']:.3f}V, "
                  f"波动范围={stats['voltage']['range']:.3f}V")
            print(f"波动强度设置: 温度={stats['intensity']['temp']:.1f}, "
                  f"电压={stats['intensity']['volt']:.1f}, "
                  f"相关性={stats['intensity']['correlation']:.1f}")

def test_wave_intensity_control():
    """测试波动强度控制功能"""
    print("\n=== 波动强度控制测试 ===")
    
    passive_comm = PassiveCommunicator("COM_TEST", 115200)
    passive_comm._initialize_base_values()
    
    # 测试不同的波动强度
    intensities = [
        (0.5, 0.5, 0.1),  # 低强度
        (1.0, 1.0, 0.3),  # 中等强度
        (1.5, 1.5, 0.5),  # 高强度
    ]
    
    for temp_int, volt_int, corr in intensities:
        print(f"\n测试强度设置: 温度={temp_int}, 电压={volt_int}, 相关性={corr}")
        passive_comm.set_wave_intensity(temp_int, volt_int, corr)
        
        # 运行一小段时间观察效果
        for _ in range(10):
            # 简化的波动更新
            import random, math
            for i in range(6):
                passive_comm.temp_wave_phases[i] += 0.1
                passive_comm.volt_wave_phases[i] += 0.1
                
                temp_wave = math.sin(passive_comm.temp_wave_phases[i]) * 0.4 * temp_int
                volt_wave = math.cos(passive_comm.volt_wave_phases[i]) * 0.15 * volt_int
                
                passive_comm.latest_data['temperature'][i] = passive_comm.base_temperatures[i] + temp_wave
                passive_comm.latest_data['voltage'][i] = passive_comm.base_voltages[i] + volt_wave
        
        stats = passive_comm.get_wave_statistics()
        if stats:
            print(f"  温度波动范围: {stats['temperature']['range']:.2f}°C")
            print(f"  电压波动范围: {stats['voltage']['range']:.3f}V")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整波动模拟测试")
    print("2. 波动强度控制测试")
    print("3. 两个测试都运行")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        test_passive_wave_simulation()
    elif choice == "2":
        test_wave_intensity_control()
    elif choice == "3":
        test_wave_intensity_control()
        test_passive_wave_simulation()
    else:
        print("无效选择，运行完整测试")
        test_passive_wave_simulation()
