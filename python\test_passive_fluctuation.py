#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
被动监听模式温度电压波动测试程序
验证被动监听模式中温度和电压的小范围波动功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from program_demo import PassiveCommunicator
import time
import threading

def test_passive_fluctuation():
    """测试被动监听模式的温度电压波动"""
    print("=" * 70)
    print("被动监听模式温度电压波动测试")
    print("=" * 70)
    
    # 创建被动通信器（不实际连接串口）
    passive_comm = PassiveCommunicator("COM1")  # 使用虚拟端口
    
    print("1. 检查初始化状态")
    print("-" * 50)
    
    # 检查是否正确初始化了波动相关变量
    required_attrs = [
        'base_temperatures', 'base_voltages', 
        'temp_wave_phases', 'volt_wave_phases', 'wave_counter'
    ]
    
    for attr in required_attrs:
        if hasattr(passive_comm, attr):
            print(f"✅ {attr} 已初始化")
        else:
            print(f"❌ {attr} 未初始化")
            return False
    
    print(f"\n初始温度: {[f'{t:.1f}' for t in passive_comm.latest_data['temperature']]}")
    print(f"初始电压: {[f'{v:.1f}' for v in passive_comm.latest_data['voltage']]}")
    
    print("\n2. 测试基础值初始化")
    print("-" * 50)
    
    # 调用基础值初始化方法
    passive_comm._initialize_base_values()
    
    print(f"基础温度: {[f'{t:.1f}' for t in passive_comm.base_temperatures]}")
    print(f"基础电压: {[f'{v:.1f}' for v in passive_comm.base_voltages]}")
    print(f"温度相位: {[f'{p:.2f}' for p in passive_comm.temp_wave_phases]}")
    print(f"电压相位: {[f'{p:.2f}' for p in passive_comm.volt_wave_phases]}")
    
    # 验证基础值范围
    temp_range_ok = all(28.0 <= t <= 32.0 for t in passive_comm.base_temperatures)
    volt_range_ok = all(11.5 <= v <= 12.5 for v in passive_comm.base_voltages)
    
    print(f"温度范围正确 (28-32°C): {'✅' if temp_range_ok else '❌'}")
    print(f"电压范围正确 (11.5-12.5V): {'✅' if volt_range_ok else '❌'}")
    
    print("\n3. 测试波动算法")
    print("-" * 50)
    
    # 模拟动画循环中的波动计算
    import random
    import math
    
    # 记录初始值
    initial_temps = passive_comm.latest_data['temperature'][:]
    initial_volts = passive_comm.latest_data['voltage'][:]
    
    print("模拟10次波动更新:")
    
    for update in range(10):
        # 模拟波动更新逻辑
        passive_comm.wave_counter += 1
        
        if passive_comm.wave_counter % 2 == 0:
            # 更新温度波动
            for i in range(6):
                passive_comm.temp_wave_phases[i] += 0.05 + random.uniform(-0.01, 0.01)
                temp_wave = math.sin(passive_comm.temp_wave_phases[i]) * 0.5
                temp_random = random.uniform(-0.1, 0.1)
                passive_comm.latest_data['temperature'][i] = passive_comm.base_temperatures[i] + temp_wave + temp_random
            
            # 更新电压波动
            for i in range(6):
                passive_comm.volt_wave_phases[i] += 0.03 + random.uniform(-0.01, 0.01)
                volt_wave = math.cos(passive_comm.volt_wave_phases[i]) * 0.2
                volt_random = random.uniform(-0.05, 0.05)
                passive_comm.latest_data['voltage'][i] = passive_comm.base_voltages[i] + volt_wave + volt_random
        
        # 显示当前值
        if update % 3 == 0:  # 每3次显示一次
            temps = [f'{t:.2f}' for t in passive_comm.latest_data['temperature']]
            volts = [f'{v:.2f}' for v in passive_comm.latest_data['voltage']]
            print(f"更新 {update+1:2d}: 温度={temps[:3]}... 电压={volts[:3]}...")
    
    # 验证波动范围
    final_temps = passive_comm.latest_data['temperature']
    final_volts = passive_comm.latest_data['voltage']
    
    print(f"\n波动范围验证:")
    print("-" * 30)
    
    temp_fluctuations = []
    volt_fluctuations = []
    
    for i in range(6):
        temp_diff = abs(final_temps[i] - passive_comm.base_temperatures[i])
        volt_diff = abs(final_volts[i] - passive_comm.base_voltages[i])
        temp_fluctuations.append(temp_diff)
        volt_fluctuations.append(volt_diff)
    
    max_temp_fluctuation = max(temp_fluctuations)
    max_volt_fluctuation = max(volt_fluctuations)
    
    print(f"最大温度波动: {max_temp_fluctuation:.2f}°C")
    print(f"最大电压波动: {max_volt_fluctuation:.2f}V")
    
    # 验证波动在合理范围内（温度±1°C，电压±0.5V）
    temp_fluctuation_ok = max_temp_fluctuation <= 1.0
    volt_fluctuation_ok = max_volt_fluctuation <= 0.5
    
    print(f"温度波动范围合理 (≤1.0°C): {'✅' if temp_fluctuation_ok else '❌'}")
    print(f"电压波动范围合理 (≤0.5V): {'✅' if volt_fluctuation_ok else '❌'}")
    
    return temp_range_ok and volt_range_ok and temp_fluctuation_ok and volt_fluctuation_ok

def test_animation_thread_simulation():
    """测试动画线程模拟"""
    print("\n" + "=" * 70)
    print("动画线程模拟测试")
    print("=" * 70)
    
    passive_comm = PassiveCommunicator("COM1")
    passive_comm._initialize_base_values()
    
    print("模拟动画线程运行5秒...")
    print("-" * 50)
    
    # 模拟动画线程
    def simulate_animation_thread():
        import random
        import math
        
        start_time = time.time()
        while time.time() - start_time < 5:  # 运行5秒
            try:
                # 更新温度和电压波动
                passive_comm.wave_counter += 1
                
                # 每50毫秒更新一次波动
                if passive_comm.wave_counter % 2 == 0:
                    # 更新温度波动
                    for i in range(6):
                        passive_comm.temp_wave_phases[i] += 0.05 + random.uniform(-0.01, 0.01)
                        temp_wave = math.sin(passive_comm.temp_wave_phases[i]) * 0.5
                        temp_random = random.uniform(-0.1, 0.1)
                        passive_comm.latest_data['temperature'][i] = passive_comm.base_temperatures[i] + temp_wave + temp_random
                    
                    # 更新电压波动
                    for i in range(6):
                        passive_comm.volt_wave_phases[i] += 0.03 + random.uniform(-0.01, 0.01)
                        volt_wave = math.cos(passive_comm.volt_wave_phases[i]) * 0.2
                        volt_random = random.uniform(-0.05, 0.05)
                        passive_comm.latest_data['voltage'][i] = passive_comm.base_voltages[i] + volt_wave + volt_random
                
                time.sleep(0.02)  # 20ms间隔
                
            except Exception as e:
                print(f"模拟线程出错: {e}")
                break
    
    # 启动模拟线程
    thread = threading.Thread(target=simulate_animation_thread)
    thread.daemon = True
    thread.start()
    
    # 监控数据变化
    sample_times = [1, 2, 3, 4, 5]
    samples = []
    
    for sample_time in sample_times:
        time.sleep(1)
        current_temps = passive_comm.latest_data['temperature'][:]
        current_volts = passive_comm.latest_data['voltage'][:]
        samples.append((current_temps, current_volts))
        
        print(f"第{sample_time}秒: 温度={current_temps[0]:.2f}°C, 电压={current_volts[0]:.2f}V")
    
    # 等待线程结束
    thread.join(timeout=1)
    
    # 验证数据是否在变化
    temp_changes = []
    volt_changes = []
    
    for i in range(1, len(samples)):
        temp_change = abs(samples[i][0][0] - samples[i-1][0][0])
        volt_change = abs(samples[i][1][0] - samples[i-1][1][0])
        temp_changes.append(temp_change)
        volt_changes.append(volt_change)
    
    avg_temp_change = sum(temp_changes) / len(temp_changes)
    avg_volt_change = sum(volt_changes) / len(volt_changes)
    
    print(f"\n平均温度变化: {avg_temp_change:.3f}°C/秒")
    print(f"平均电压变化: {avg_volt_change:.3f}V/秒")
    
    # 验证是否有合理的变化
    temp_changing = avg_temp_change > 0.001  # 至少有微小变化
    volt_changing = avg_volt_change > 0.001
    
    print(f"温度正在波动: {'✅' if temp_changing else '❌'}")
    print(f"电压正在波动: {'✅' if volt_changing else '❌'}")
    
    return temp_changing and volt_changing

def main():
    """主测试函数"""
    print("被动监听模式温度电压波动功能测试")
    print("=" * 70)
    print("验证被动监听模式中温度和电压的小范围波动功能")
    print("=" * 70)
    
    try:
        # 执行测试
        test1_result = test_passive_fluctuation()
        test2_result = test_animation_thread_simulation()
        
        # 汇总结果
        print("\n" + "=" * 70)
        print("测试结果汇总")
        print("=" * 70)
        
        if test1_result and test2_result:
            print("🎉 所有测试通过！被动监听模式温度电压波动功能正常！")
            print("\n✅ 功能确认:")
            print("  • 温度和电压基础值初始化正确")
            print("  • 波动算法工作正常")
            print("  • 波动范围在合理区间内")
            print("  • 动画线程模拟运行正常")
            print("  • 数据实时更新")
            
            print("\n📊 波动特性:")
            print("  • 温度波动: ±0.5°C (正弦波) + ±0.1°C (随机)")
            print("  • 电压波动: ±0.2V (余弦波) + ±0.05V (随机)")
            print("  • 更新频率: 每20毫秒")
            print("  • 每个舵机独立相位，产生不同步效果")
            
            print("\n🎨 用户体验:")
            print("  • 温度和电压显示更加真实")
            print("  • 小范围波动模拟实际工作状态")
            print("  • 平滑的波动效果，无突变")
            print("  • 每个舵机的波动略有不同，更自然")
            
        else:
            print("❌ 部分测试失败，请检查波动功能实现")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
