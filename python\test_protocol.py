#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新协议测试程序
用于测试物块颜色和机械臂状态协议的发送和接收功能
"""

import serial
import time
import threading

class ProtocolTester:
    """新协议测试类"""
    
    def __init__(self, port, baudrate=115200):
        """初始化测试器
        
        Args:
            port: 串口名称
            baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        
    def calc_protocol_checksum(self, cmd, data):
        """计算新协议的校验和
        
        Args:
            cmd: 指令字节
            data: 数据字节
            
        Returns:
            校验和字节
        """
        return (cmd + data) & 0xFF
    
    def create_protocol_packet(self, cmd, data):
        """创建新协议数据包
        
        Args:
            cmd: 指令 (0x01=物块颜色, 0x02=机械臂状态)
            data: 数据
            
        Returns:
            完整的数据包字节
        """
        checksum = self.calc_protocol_checksum(cmd, data)
        packet = bytes([0x77, 0x77, cmd, data, checksum])
        return packet
    
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            print(f"成功连接到串口 {self.port}")
            return True
        except Exception as e:
            print(f"连接串口失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已断开")
    
    def send_block_color(self, color):
        """发送物块颜色指令
        
        Args:
            color: 颜色代码 (0=未识别, 1=红色, 2=绿色, 3=蓝色)
        """
        if not self.ser or not self.ser.is_open:
            print("串口未连接")
            return False
            
        try:
            packet = self.create_protocol_packet(0x01, color)
            self.ser.write(packet)
            
            color_names = {0: "未识别", 1: "红色物块", 2: "绿色物块", 3: "蓝色物块"}
            color_name = color_names.get(color, f"未知颜色({color})")
            print(f"发送物块颜色指令: {color_name}")
            print(f"数据包: {' '.join([f'{b:02X}' for b in packet])}")
            return True
        except Exception as e:
            print(f"发送物块颜色指令失败: {e}")
            return False
    
    def send_arm_status(self, status):
        """发送机械臂状态指令
        
        Args:
            status: 状态代码 (0=检测, 1=抓取, 2=运输, 3=分拣)
        """
        if not self.ser or not self.ser.is_open:
            print("串口未连接")
            return False
            
        try:
            packet = self.create_protocol_packet(0x02, status)
            self.ser.write(packet)
            
            status_names = {0: "检测状态", 1: "抓取状态", 2: "运输状态", 3: "分拣状态"}
            status_name = status_names.get(status, f"未知状态({status})")
            print(f"发送机械臂状态指令: {status_name}")
            print(f"数据包: {' '.join([f'{b:02X}' for b in packet])}")
            return True
        except Exception as e:
            print(f"发送机械臂状态指令失败: {e}")
            return False
    
    def auto_test(self):
        """自动测试模式"""
        print("开始自动测试模式...")
        print("将循环发送不同的物块颜色和机械臂状态指令")
        print("按 Ctrl+C 停止测试")
        
        self.running = True
        counter = 0
        
        try:
            while self.running:
                # 每5秒发送一次物块颜色指令
                if counter % 5 == 0:
                    color = (counter // 5) % 4  # 循环 0, 1, 2, 3
                    self.send_block_color(color)
                
                # 每7秒发送一次机械臂状态指令
                if counter % 7 == 0:
                    status = (counter // 7) % 4  # 循环 0, 1, 2, 3
                    self.send_arm_status(status)
                
                time.sleep(1)
                counter += 1
                
        except KeyboardInterrupt:
            print("\n测试已停止")
            self.running = False

def main():
    """主函数"""
    print("新协议测试程序")
    print("=" * 50)
    
    # 获取串口列表
    import serial.tools.list_ports
    ports = [p.device for p in serial.tools.list_ports.comports()]
    
    if not ports:
        print("未找到可用的串口")
        return
    
    print("可用串口:")
    for i, port in enumerate(ports):
        print(f"{i+1}. {port}")
    
    # 选择串口
    try:
        choice = int(input(f"请选择串口 (1-{len(ports)}): ")) - 1
        if choice < 0 or choice >= len(ports):
            print("无效选择")
            return
        selected_port = ports[choice]
    except ValueError:
        print("无效输入")
        return
    
    # 创建测试器
    tester = ProtocolTester(selected_port)
    
    if not tester.connect():
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("请选择测试模式:")
            print("1. 发送物块颜色指令")
            print("2. 发送机械臂状态指令")
            print("3. 自动测试模式")
            print("4. 退出")
            
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == "1":
                print("\n物块颜色选项:")
                print("0. 未识别")
                print("1. 红色物块")
                print("2. 绿色物块")
                print("3. 蓝色物块")
                try:
                    color = int(input("请输入颜色代码 (0-3): "))
                    if 0 <= color <= 3:
                        tester.send_block_color(color)
                    else:
                        print("无效的颜色代码")
                except ValueError:
                    print("无效输入")
            
            elif choice == "2":
                print("\n机械臂状态选项:")
                print("0. 检测状态")
                print("1. 抓取状态")
                print("2. 运输状态")
                print("3. 分拣状态")
                try:
                    status = int(input("请输入状态代码 (0-3): "))
                    if 0 <= status <= 3:
                        tester.send_arm_status(status)
                    else:
                        print("无效的状态代码")
                except ValueError:
                    print("无效输入")
            
            elif choice == "3":
                tester.auto_test()
            
            elif choice == "4":
                break
            
            else:
                print("无效选择")
    
    except KeyboardInterrupt:
        print("\n程序已停止")
    
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()
