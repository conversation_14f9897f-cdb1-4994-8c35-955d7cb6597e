# 被动监听模式温度电压波动功能说明

## 功能概述

在被动监听模式中，我们为温度和电压添加了丰富的小范围波动动态演示功能，模拟真实舵机工作时的温度和电压变化特性。

## 主要特性

### 1. 多层次波动模拟

#### 温度波动组成：
- **主要波动**: 中频正弦波，振幅±0.4°C，模拟舵机工作时的主要温度变化
- **次级波动**: 高频小幅波动，振幅±0.15°C，模拟快速的温度脉动
- **长期趋势**: 低频大周期变化，振幅±0.8°C，模拟环境温度的缓慢变化
- **环境影响**: 模拟环境温度变化，振幅±0.3°C
- **运动发热**: 运动中的舵机温度略微上升0.1-0.3°C
- **随机噪声**: 模拟真实传感器噪声，振幅±0.08°C

#### 电压波动组成：
- **主要波动**: 中频余弦波，振幅±0.15V，模拟电源的主要波动
- **次级波动**: 高频小幅波动，振幅±0.08V，模拟电路中的高频噪声
- **长期趋势**: 低频大周期变化，振幅±0.25V，模拟电源的长期稳定性
- **温度相关性**: 温度升高时电压略微下降，模拟温度对电路的影响
- **负载影响**: 模拟负载变化对电压的影响，振幅±0.1V
- **电源波动**: 模拟电源不稳定性，振幅±0.15V
- **随机噪声**: 模拟真实电压测量噪声，振幅±0.03V

### 2. 智能相关性模拟

- **温度电压相关性**: 通过可调节的相关性因子（默认0.3）模拟温度和电压之间的关系
- **运动状态影响**: 正在运动的舵机温度会略微上升，模拟机械摩擦产生的热量
- **环境因素**: 统一的环境温度变化影响所有舵机

### 3. 动态参数控制

#### 波动强度控制：
```python
# 设置波动强度
passive_comm.set_wave_intensity(
    temp_intensity=1.0,    # 温度波动强度 (0.0-2.0)
    volt_intensity=1.0,    # 电压波动强度 (0.0-2.0)
    correlation=0.3        # 温度电压相关性 (0.0-1.0)
)
```

#### 实时统计信息：
```python
# 获取波动统计
stats = passive_comm.get_wave_statistics()
```

### 4. 安全范围限制

- **温度范围**: 25.0°C - 40.0°C
- **电压范围**: 10.5V - 13.5V
- **基础值范围**: 
  - 温度: 28-32°C
  - 电压: 11.5-12.5V

## 技术实现

### 1. 相位管理
每个舵机都有独立的相位参数：
- `temp_wave_phases[i]`: 主要温度波动相位
- `temp_secondary_phases[i]`: 次级温度波动相位
- `temp_trend_phases[i]`: 长期温度趋势相位
- `volt_wave_phases[i]`: 主要电压波动相位
- `volt_secondary_phases[i]`: 次级电压波动相位
- `volt_trend_phases[i]`: 长期电压趋势相位

### 2. 环境模拟
全局环境参数：
- `ambient_temp_phase`: 环境温度相位
- `load_simulation_phase`: 负载模拟相位
- `power_fluctuation_phase`: 电源波动相位

### 3. 更新频率
- 波动更新频率: 50Hz (每20ms更新一次)
- 统计输出频率: 每5秒输出一次统计信息
- 相位更新速度: 不同频率的相位以不同速度更新

## 使用方法

### 1. 在GUI中使用
1. 启动程序：`python program_demo.py`
2. 选择通信模式为"被动监听"
3. 选择串口并连接
4. 观察温度和电压的实时波动

### 2. 编程接口使用
```python
from program_demo import PassiveCommunicator

# 创建被动通信实例
passive_comm = PassiveCommunicator("COM3", 115200)

# 启动监听
passive_comm.start()

# 调整波动强度
passive_comm.set_wave_intensity(1.5, 1.2, 0.4)

# 获取统计信息
stats = passive_comm.get_wave_statistics()
print(f"温度范围: {stats['temperature']['range']:.2f}°C")
print(f"电压范围: {stats['voltage']['range']:.3f}V")
```

### 3. 测试脚本
运行测试脚本查看波动效果：
```bash
python simple_wave_test.py
```

## 波动效果展示

### 典型波动范围：
- **温度波动范围**: 1.5-2.5°C
- **电压波动范围**: 1.0-1.5V
- **更新频率**: 50Hz
- **波动周期**: 多重周期叠加，从几秒到几分钟不等

### 实时监控输出示例：
```
--- 2.0秒 (第100次更新) ---
舵机1: 29.07°C, 12.329V, 运动中
舵机2: 30.76°C, 11.318V, 运动中
舵机3: 29.24°C, 11.204V, 静止
舵机4: 30.01°C, 12.288V, 静止
舵机5: 29.22°C, 12.582V, 静止
舵机6: 29.54°C, 11.498V, 静止
温度范围: 29.07°C - 30.76°C
电压范围: 11.204V - 12.582V
```

## 应用场景

1. **系统演示**: 在没有真实硬件时展示系统的监控能力
2. **算法测试**: 为数据处理算法提供真实的测试数据
3. **界面调试**: 测试UI界面对动态数据的响应
4. **培训教学**: 展示舵机系统的工作特性
5. **压力测试**: 测试系统在连续数据流下的稳定性

## 扩展功能

未来可以考虑添加：
1. **故障模拟**: 模拟温度过高、电压异常等故障情况
2. **负载曲线**: 根据舵机角度变化模拟不同的负载情况
3. **季节变化**: 模拟不同季节的环境温度影响
4. **老化效应**: 模拟舵机使用时间对性能的影响
5. **批次差异**: 模拟不同批次舵机的性能差异
